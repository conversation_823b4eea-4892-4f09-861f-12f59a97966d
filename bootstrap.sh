#!/bin/bash

# 此脚本用于启动DeerFlow的后端服务器和Web UI
# 支持开发模式和生产模式
# 当用户按下Ctrl+C时，会同时终止两个服务

# 检查命令行参数是否为开发模式
# 支持多种开发模式的参数形式：--dev, -d, dev, development
if [ "$1" = "--dev" -o "$1" = "-d" -o "$1" = "dev" -o "$1" = "development" ]; then
  # 开发模式：启用热重载
  echo -e "Starting DeerFlow in [DEVELOPMENT] mode...\n"
  # 使用uv运行后端服务器，启用热重载功能
  uv run server.py --reload & SERVER_PID=$$!
  # 进入web目录并启动前端开发服务器
  cd web && pnpm dev & WEB_PID=$$!
  # 设置信号处理器：当收到SIGINT或SIGTERM信号时终止两个进程
  trap "kill $$SERVER_PID $$WEB_PID" SIGINT SIGTERM
  # 等待子进程完成
  wait
else
  # 生产模式
  echo -e "Starting DeerFlow in [PRODUCTION] mode...\n"
  # 使用uv运行后端服务器
  uv run server.py & SERVER_PID=$$!
  # 进入web目录并启动前端生产服务器
  cd web && pnpm start & WEB_PID=$$!
  # 设置信号处理器：当收到SIGINT或SIGTERM信号时终止两个进程
  trap "kill $$SERVER_PID $$WEB_PID" SIGINT SIGTERM
  # 等待子进程完成
  wait
fi
