# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Entry point script for the DeerFlow project.
"""

import argparse
import asyncio

from InquirerPy import inquirer

from src.config.questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN
from src.workflow import run_agent_workflow_async


def ask(
    question,
    debug=False,
    max_plan_iterations=1,
    max_step_num=3,
    enable_background_investigation=True,
):
    """运行代理工作流来处理给定的问题。

    这个函数是处理用户查询的核心函数，它负责:
    1. 接收用户的查询和配置参数
    2. 使用asyncio异步运行工作流
    3. 调用底层的run_agent_workflow_async函数执行实际的研究和分析工作

    参数说明:
        question (str): 
            用户的查询或请求内容。这可以是任何研究问题或信息请求
        debug (bool, 可选): 
            如果设为True,将启用调试级别的日志记录,方便开发和故障排查
            默认值: False
        max_plan_iterations (int, 可选):
            计划迭代的最大次数。每次迭代都会生成和优化研究计划
            较大的值会导致更全面但更耗时的分析
            默认值: 1
        max_step_num (int, 可选):
            研究计划中允许的最大步骤数。限制步骤数可以控制研究的范围和深度
            默认值: 3
        enable_background_investigation (bool, 可选):
            如果为True, 在制定计划前会进行网络搜索以增强上下文理解
            这可以提供更全面的研究背景，但会增加处理时间
            默认值: True
    工作流程:
    1. 验证输入参数的有效性
    2. 使用asyncio.run启动异步执行环境
    3. 调用run_agent_workflow_async执行实际的研究工作
    4. 处理完成后自动清理资源

    注意:
    - 这个函数会阻塞直到整个工作流完成
    - 所有参数都会直接传递给底层的异步函数
    - 异常会被传播到调用者
    """
    asyncio.run(
        run_agent_workflow_async(
            user_input=question,
            debug=debug,
            max_plan_iterations=max_plan_iterations,
            max_step_num=max_step_num,
            enable_background_investigation=enable_background_investigation,
        )
    )


def main(
    debug=False,
    max_plan_iterations=1,
    max_step_num=3,
    enable_background_investigation=True,
):
    """Interactive mode with built-in questions.

    Args:
        enable_background_investigation: If True, performs web search before planning to enhance context
        debug: If True, enables debug level logging
        max_plan_iterations: Maximum number of plan iterations
        max_step_num: Maximum number of steps in a plan
    """
    # First select language
    language = inquirer.select(
        message="Select language / 选择语言:",
        choices=["English", "中文"],
    ).execute()

    # Choose questions based on language
    questions = (
        BUILT_IN_QUESTIONS if language == "English" else BUILT_IN_QUESTIONS_ZH_CN
    )
    ask_own_option = (
        "[Ask my own question]" if language == "English" else "[自定义问题]"
    )

    # Select a question
    initial_question = inquirer.select(
        message=(
            "What do you want to know?" if language == "English" else "您想了解什么?"
        ),
        choices=[ask_own_option] + questions,
    ).execute()

    if initial_question == ask_own_option:
        initial_question = inquirer.text(
            message=(
                "What do you want to know?"
                if language == "English"
                else "您想了解什么?"
            ),
        ).execute()

    # Pass all parameters to ask function
    ask(
        question=initial_question,
        debug=debug,
        max_plan_iterations=max_plan_iterations,
        max_step_num=max_step_num,
        enable_background_investigation=enable_background_investigation,
    )


if __name__ == "__main__":
    # 设置命令行参数解析器
    parser = argparse.ArgumentParser(description="Run the Deer")
    # 设置可接受的命令行参数:
    # 1. query: 可选参数，用于接收研究查询内容
    parser.add_argument("query", nargs="*", help="The query to process")
    
    # 2. --interactive: 布尔标志，启用交互模式
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Run in interactive mode with built-in questions",
    )
    
    # 3. --max_plan_iterations: 整数参数，设置最大计划迭代次数
    parser.add_argument(
        "--max_plan_iterations",
        type=int,
        default=1,
        help="Maximum number of plan iterations (default: 1)",
    )
    
    # 4. --max_step_num: 整数参数，设置计划中的最大步骤数
    parser.add_argument(
        "--max_step_num",
        type=int,
        default=3,
        help="Maximum number of steps in a plan (default: 3)",
    )
    
    # 5. --debug: 布尔标志，启用调试日志
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    # 6. --no-background-investigation: 布尔标志，禁用背景调查
    parser.add_argument(
        "--no-background-investigation",
        action="store_false",
        dest="enable_background_investigation",
        help="Disable background investigation before planning",
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 根据运行模式选择执行路径:
    if args.interactive:
        # 交互模式: 调用main函数，传入所有命令行参数
        main(
            debug=args.debug,
            max_plan_iterations=args.max_plan_iterations,
            max_step_num=args.max_step_num,
            enable_background_investigation=args.enable_background_investigation,
        )
    else:
        # 非交互模式: 处理用户输入
        if args.query:
            # 如果命令行提供了查询参数，将其组合成完整查询字符串
            user_query = " ".join(args.query)
        else:
            # 如果没有提供查询参数，循环等待用户输入
            while True:
                user_query = input("Enter your query: ")
                if user_query is not None and user_query != "":
                    break

        # 使用处理好的查询和其他参数调用ask函数执行工作流
        ask(
            question=user_query,
            debug=args.debug,
            max_plan_iterations=args.max_plan_iterations,
            max_step_num=args.max_step_num,
            enable_background_investigation=args.enable_background_investigation,
        )
