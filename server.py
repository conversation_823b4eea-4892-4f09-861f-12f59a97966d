# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Server script for running the DeerFlow API.
"""

# 导入必要的Python标准库和第三方库
import argparse  # 用于解析命令行参数
import logging  # 用于日志记录
import signal   # 用于处理系统信号
import sys      # 用于系统相关操作
import uvicorn  # ASGI服务器，用于运行FastAPI应用

# 配置日志记录
# 设置日志格式为: 时间 - 模块名 - 日志级别 - 日志消息
logging.basicConfig(
    level=logging.INFO,  # 设置默认日志级别为INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

# 获取当前模块的logger实例
logger = logging.getLogger(__name__)


def handle_shutdown(signum, frame):
    """
    处理优雅关闭的函数
    当收到SIGTERM或SIGINT信号时(如按Ctrl+C)，记录日志并安全退出
    
    Args:
        signum: 信号编号
        frame: 当前栈帧
    """
    logger.info("Received shutdown signal. Starting graceful shutdown...")
    sys.exit(0)

# 注册信号处理器
# SIGTERM: 终止信号，通常用于请求程序终止
# SIGINT: 中断信号，通常是用户按 Ctrl+C 发出的信号
signal.signal(signal.SIGTERM, handle_shutdown)
signal.signal(signal.SIGINT, handle_shutdown)

if __name__ == "__main__":
    # 创建命令行参数解析器，用于解析启动服务器时的配置选项
    parser = argparse.ArgumentParser(description="Run the DeerFlow API server")
    
    # 添加--reload参数：布尔标志，用于启用代码热重载功能，每次代码文件变更都会重新导入模块，graph会重新构建
    parser.add_argument(
        "--reload",
        action="store_true",  # 设置为标志参数，存在则为True
        help="Enable auto-reload (default: True except on Windows)",
    )
    
    # 添加--host参数：指定服务器绑定的主机地址
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",  # 默认绑定到localhost
        help="Host to bind the server to (default: localhost)",
    )
    
    # 添加--port参数：指定服务器监听的端口号
    parser.add_argument(
        "--port",
        type=int,
        default=8000,  # 默认使用8000端口
        help="Port to bind the server to (default: 8000)",
    )
    
    # 添加--log-level参数：设置日志级别
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",  # 默认使用info级别
        choices=["debug", "info", "warning", "error", "critical"],  # 可选的日志级别
        help="Log level (default: info)",
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 确定是否启用热重载
    reload = False
    if args.reload:
        reload = True

    try:
        # 记录服务器启动信息
        logger.info(f"Starting DeerFlow API server on {args.host}:{args.port}")
        
        # 使用 uvicorn 启动服务器
        # src.server:app 指定了应用程序的导入路径
        # host和port指定服务器绑定的地址和端口
        # reload启用热重载功能
        # log_level设置 uvicorn 的日志级别
        uvicorn.run(
            "src.server:app",
            host=args.host,
            port=args.port,
            reload=reload,
            log_level=args.log_level,
        )
    except Exception as e:
        # 如果启动失败，记录错误信息并以错误状态退出
        logger.error(f"Failed to start server: {str(e)}")
        sys.exit(1)
