# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

# 导入必要的依赖
from langgraph.graph import StateGraph, START, END  # 导入状态图相关的类和常量
from langgraph.checkpoint.memory import MemorySaver  # 导入内存保存器
from src.prompts.planner_model import StepType  # 导入步骤类型枚举

# 导入本地模块
from .types import State  # 导入状态类型定义
from .nodes import (  # 导入各个节点的实现
    coordinator_node,      # 协调器节点
    planner_node,         # 规划器节点
    reporter_node,        # 报告生成器节点
    research_team_node,   # 研究团队节点
    researcher_node,      # 研究员节点
    coder_node,          # 代码分析员节点
    human_feedback_node,  # 人类反馈节点
    background_investigation_node,  # 背景调查节点
)


def continue_to_running_research_team(state: State):
    """
    决定研究团队的下一步行动
    Args:
        state: 当前状态对象
    Returns:
        str: 下一个节点的名称
    """
    current_plan = state.get("current_plan")
    # 如果没有计划或计划步骤为空，返回规划器
    if not current_plan or not current_plan.steps:
        return "planner"

    # 如果所有步骤都已完成，返回规划器
    if all(step.execution_res for step in current_plan.steps):
        return "planner"

    # 查找第一个未完成的步骤
    incomplete_step = None
    for step in current_plan.steps:
        if not step.execution_res:
            incomplete_step = step
            break

    # 如果没有找到未完成步骤，返回规划器
    if not incomplete_step:
        return "planner"

    # 根据步骤类型返回对应的执行者
    if incomplete_step.step_type == StepType.RESEARCH:
        return "researcher"  # 研究类型任务交给研究员
    if incomplete_step.step_type == StepType.PROCESSING:
        return "coder"      # 处理类型任务交给代码分析员
    return "planner"        # 其他情况返回规划器


def _build_base_graph():
    """构建并返回基础状态图，包含所有节点和边"""
    # 创建状态图实例
    builder = StateGraph(State)
    
    # 添加所有节点和边
    builder.add_edge(START, "coordinator")  # 从起点连接到协调器
    builder.add_node("coordinator", coordinator_node)  # 添加协调器节点
    builder.add_node("background_investigator", background_investigation_node)  # 添加背景调查节点
    builder.add_node("planner", planner_node)  # 添加规划器节点
    builder.add_node("reporter", reporter_node)  # 添加报告生成器节点
    builder.add_node("research_team", research_team_node)  # 添加研究团队节点
    builder.add_node("researcher", researcher_node)  # 添加研究员节点
    builder.add_node("coder", coder_node)  # 添加代码分析员节点
    builder.add_node("human_feedback", human_feedback_node)  # 添加人类反馈节点
    
    # 添加边连接各个节点
    builder.add_edge("background_investigator", "planner")  # 背景调查连接到规划器
    # 添加条件边，根据continue_to_running_research_team函数的返回值决定下一步
    builder.add_conditional_edges(
        "research_team",
        continue_to_running_research_team,
        ["planner", "researcher", "coder"],
    )
    builder.add_edge("reporter", END)  # 报告生成器连接到终点
    return builder


def build_graph_with_memory():
    """构建并返回带有内存功能的智能体工作流图"""
    # 创建内存保存器实例用于保存对话历史
    # TODO: 后续需要支持 SQLite/PostgreSQL
    memory = MemorySaver()

    # 构建状态图并返回带有内存检查点的编译结果
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)


def build_graph():
    """构建并返回不带内存功能的智能体工作流图"""
    # 构建状态图并返回编译结果
    builder = _build_base_graph()
    return builder.compile()


# 创建默认的工作流图实例
graph = build_graph()
