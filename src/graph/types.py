# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT


from langgraph.graph import MessagesState

from src.prompts.planner_model import Plan
from src.rag import Resource


class State(MessagesState):
    """State类继承自MessagesState, 用于管理智能体系统的状态信息。
    
    主要状态变量包括:
    - locale: 语言地区设置，默认为'en-US'
    - research_topic: 研究主题，初始为空字符串
    - observations: 观察结果列表，用于存储研究过程中的发现
    - resources: Resource类型的资源列表, 存储研究所需的参考资料
    - plan_iterations: 计划迭代次数, 从0开始计数
    - current_plan: 当前执行的计划, 可以是Plan对象或字符串
    - final_report: 最终研究报告内容
    - auto_accepted_plan: 是否自动接受计划的标志
    - enable_background_investigation: 是否启用背景调查功能，默认开启
    - background_investigation_results: 背景调查的结果存储
    """
    # Runtime Variables - 运行时状态变量
    locale: str = "en-US"  # 语言地区设置
    research_topic: str = ""  # 研究主题
    observations: list[str] = []  # 观察结果列表
    resources: list[Resource] = []  # 资源列表
    plan_iterations: int = 0  # 计划迭代计数器
    current_plan: Plan | str = None  # 当前执行的计划
    final_report: str = ""  # 最终报告
    auto_accepted_plan: bool = False  # 自动接受计划标志
    enable_background_investigation: bool = True  # 背景调查开关
    background_investigation_results: str = None  # 背景调查结果
