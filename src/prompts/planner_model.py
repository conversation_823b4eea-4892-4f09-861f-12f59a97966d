# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class StepType(str, Enum):
    RESEARCH = "research"
    PROCESSING = "processing"


# Step类定义了研究/分析计划中的单个步骤
class Step(BaseModel):
    # 标识该步骤是否需要进行网络搜索，必须显式设置
    need_search: bool = Field(..., description="Must be explicitly set for each step")
    
    # 步骤的标题
    title: str
    
    # 详细描述需要收集的具体数据内容
    description: str = Field(..., description="Specify exactly what data to collect")
    
    # 步骤的类型，可以是研究(research)或处理(processing)
    step_type: StepType = Field(..., description="Indicates the nature of the step")
    
    # 步骤的执行结果，初始为None，执行完成后会被更新
    execution_res: Optional[str] = Field(
        default=None, description="The Step execution result"
    )


# Plan类定义了一个研究/分析计划的数据模型
class Plan(BaseModel):
    # 语言地区设置，如'en-US'或'zh-CN'，基于用户的语言偏好
    locale: str = Field(
        ..., description="e.g. 'en-US' or 'zh-CN', based on the user's language"
    )
    # 标识是否已有足够的上下文信息来回答问题
    has_enough_context: bool
    # 记录当前计划的思考过程和推理
    thought: str
    # 计划的标题
    title: str
    # 存储计划中的具体步骤列表，包含研究和处理步骤
    steps: List[Step] = Field(
        default_factory=list,
        description="Research & Processing steps to get more context",
    )

    # 配置类，提供JSON schema的额外信息和示例
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    # 示例：一个AI市场研究计划
                    "has_enough_context": False,  # 表示需要收集更多信息
                    "thought": (  # 计划的思考过程
                        "To understand the current market trends in AI, we need to gather comprehensive information."
                    ),
                    "title": "AI Market Research Plan",  # 计划标题
                    "steps": [
                        {   # 示例步骤：市场分析
                            "need_search": True,  # 需要进行网络搜索
                            "title": "Current AI Market Analysis",
                            "description": (  # 详细描述需要收集的数据
                                "Collect data on market size, growth rates, major players, and investment trends in AI sector."
                            ),
                            "step_type": "research",  # 步骤类型为研究
                        }
                    ],
                }
            ]
        }
