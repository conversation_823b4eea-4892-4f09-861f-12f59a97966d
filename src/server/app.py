# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import base64
import json
import logging
import os
from typing import Annotated, List, cast
from uuid import uuid4

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langgraph.types import Command

from src.config.configuration import get_recursion_limit
from src.config.report_style import ReportStyle
from src.config.tools import SELECTED_RAG_PROVIDER
from src.graph.builder import build_graph_with_memory
from src.llms.llm import get_configured_llm_models
from src.podcast.graph.builder import build_graph as build_podcast_graph
from src.ppt.graph.builder import build_graph as build_ppt_graph
from src.prompt_enhancer.graph.builder import build_graph as build_prompt_enhancer_graph
from src.prose.graph.builder import build_graph as build_prose_graph
from src.rag.builder import build_retriever
from src.rag.retriever import Resource
from src.server.chat_request import (
    ChatRequest,
    EnhancePromptRequest,
    GeneratePodcastRequest,
    GeneratePPTRequest,
    GenerateProseRequest,
    TTSRequest,
)
from src.server.config_request import ConfigResponse
from src.server.mcp_request import MCPServerMetadataRequest, MCPServerMetadataResponse
from src.server.mcp_utils import load_mcp_tools
from src.server.rag_request import (
    RAGConfigResponse,
    RAGResourceRequest,
    RAGResourcesResponse,
)
from src.tools import VolcengineTTS

logger = logging.getLogger(__name__)

# 定义服务器内部错误的详细信息常量
INTERNAL_SERVER_ERROR_DETAIL = "Internal Server Error"

# 创建 FastAPI 应用实例
app = FastAPI(
    title="DeerFlow API",          # API标题
    description="API for Deer",    # API描述
    version="0.1.0",               # API版本号
)

# 配置CORS(跨源资源共享)中间件
# 从环境变量加载允许的源，以提高安全性和跨环境灵活性
# 默认值为http://localhost:3000
allowed_origins_str = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000")
# 将字符串分割成列表，并去除每个源的首尾空格
allowed_origins = [origin.strip() for origin in allowed_origins_str.split(",")]

# 记录允许的源到日志
logger.info(f"Allowed origins: {allowed_origins}")

# 添加 CORS 中间件到应用
# 解决跨域问题，前后端分离架构: 后端运行端口和前端运行端口不同，浏览器同源策略会阻止跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,  # 限制允许的源
    allow_credentials=True,         # 允许前端发送认证信息(cookies,authorization headers)
    allow_methods=["GET", "POST", "OPTIONS"],  # 允许的HTTP方法
    allow_headers=["*"],           # 允许所有请求头,可以进一步限制,支持自定义 headers
)

# 构建带有记忆功能的工作流图实例, 并将其赋值给 graph 变量
# 这行代码位于模块的顶层顶层作用于，不在任何函数或类内部，因此会在 src.server.app 模块首次被导入时立即执行
# 确保工作流图只构建一次，提高了 API 响应性能
graph = build_graph_with_memory()


@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    """
    处理聊天流式请求的端点
    功能说明:
    - 提供流式聊天接口, 支持实时返回AI助手的回复
    - 支持MCP(Model Control Protocol)服务器配置
    - 支持自定义线程ID和各种参数配置
    参数解析:
    - request: ChatRequest对象,包含所有请求参数
    """
    # 检查是否启用 MCP 服务器配置
    # 从环境变量获取配置, 支持多种格式的 true 值
    mcp_enabled = os.getenv("ENABLE_MCP_SERVER_CONFIGURATION", "false").lower() in [
        "true", 
        "1",
        "yes",
    ]

    # 验证 MCP 设置
    # 如果提供了 MCP 设置但功能未启用, 则返回403错误
    if request.mcp_settings and not mcp_enabled:
        raise HTTPException(
            status_code=403,
            detail="MCP server configuration is disabled. Set ENABLE_MCP_SERVER_CONFIGURATION=true to enable MCP features.",
        )

    # 处理线程 ID
    # 如果是默认线程 ID,则生成新的 UUID
    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    
    # 返回流式响应
    # 调用工作流生成器处理请求并返回SSE格式的响应
    return StreamingResponse(
        _astream_workflow_generator(
            request.model_dump()["messages"],     # 消息历史
            thread_id,                            # 会话线程ID
            request.resources,                    # 资源列表
            request.max_plan_iterations,          # 最大计划迭代次数
            request.max_step_num,                 # 最大步骤数
            request.max_search_results,           # 最大搜索结果数
            request.auto_accepted_plan,           # 是否自动接受计划
            request.interrupt_feedback,           # 中断反馈
            request.mcp_settings if mcp_enabled else {},  # MCP设置(如果启用)
            request.enable_background_investigation,  # 是否启用背景调查
            request.report_style,                 # 报告风格
            request.enable_deep_thinking,         # 是否启用深度思考
        ),
        media_type="text/event-stream",          # 设置响应类型为SSE
    )


async def _astream_workflow_generator(
    messages: List[dict],              # 聊天消息历史列表
    thread_id: str,                    # 会话线程ID
    resources: List[Resource],         # 资源列表
    max_plan_iterations: int,          # 最大计划迭代次数
    max_step_num: int,                 # 最大步骤数
    max_search_results: int,           # 最大搜索结果数
    auto_accepted_plan: bool,          # 是否自动接受计划
    interrupt_feedback: str,           # 中断反馈信息
    mcp_settings: dict,                # MCP服务器配置
    enable_background_investigation: bool,  # 是否启用背景调查
    report_style: ReportStyle,         # 报告风格
    enable_deep_thinking: bool,        # 是否启用深度思考
):
    """
    异步生成工作流事件流的生成器函数
    处理聊天消息并生成相应的事件流响应
    """
    # 构建初始输入数据
    input_ = {
        "messages": messages,          # 历史消息
        "plan_iterations": 0,          # 计划迭代计数器
        "final_report": "",           # 最终报告内容
        "current_plan": None,         # 当前计划
        "observations": [],           # 观察结果列表
        "auto_accepted_plan": auto_accepted_plan,  # 是否自动接受计划
        "enable_background_investigation": enable_background_investigation,  # 是否启用背景调查
        "research_topic": messages[-1]["content"] if messages else "",  # 研究主题(最后一条消息)
    }

    # 处理中断反馈情况
    if not auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        # 将最后一条消息添加到恢复消息中
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)  # 创建恢复命令

    # 异步迭代工作流生成的事件流
    async for agent, _, event_data in graph.astream(
        input_,  # 输入数据
        config={  # 配置参数
            "thread_id": thread_id,
            "resources": resources,
            "max_plan_iterations": max_plan_iterations,
            "max_step_num": max_step_num,
            "max_search_results": max_search_results,
            "mcp_settings": mcp_settings,
            "report_style": report_style.value,
            "enable_deep_thinking": enable_deep_thinking,
            "recursion_limit": get_recursion_limit(),
        },
        stream_mode=["messages", "updates"],  # 流模式设置
        subgraphs=True,  # 启用子图
    ):
        # 处理中断事件
        if isinstance(event_data, dict):
            if "__interrupt__" in event_data:
                # 生成中断事件响应
                yield _make_event(
                    "interrupt",
                    {
                        "thread_id": thread_id,
                        "id": event_data["__interrupt__"][0].ns[0],
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [  # 中断选项
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
            continue

        # 解析消息块和元数据
        message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, any]], event_data
        )

        # 处理代理名称
        agent_name = "planner"  # 默认代理名称
        if agent and len(agent) > 0:
            # 从代理标识符中提取名称
            agent_name = agent[0].split(":")[0] if ":" in agent[0] else agent[0]

        # 构建事件流消息
        event_stream_message: dict[str, any] = {
            "thread_id": thread_id,
            "agent": agent_name,
            "id": message_chunk.id,
            "role": "assistant",
            "content": message_chunk.content,
        }

        # 添加推理内容(如果存在)
        if message_chunk.additional_kwargs.get("reasoning_content"):
            event_stream_message["reasoning_content"] = message_chunk.additional_kwargs[
                "reasoning_content"
            ]

        # 添加完成原因(如果存在)
        if message_chunk.response_metadata.get("finish_reason"):
            event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                "finish_reason"
            )

        # 根据消息类型生成不同的事件
        if isinstance(message_chunk, ToolMessage):
            # 工具消息 - 返回工具调用结果
            event_stream_message["tool_call_id"] = message_chunk.tool_call_id
            yield _make_event("tool_call_result", event_stream_message)
        elif isinstance(message_chunk, AIMessageChunk):
            # AI消息处理
            if message_chunk.tool_calls:
                # 工具调用
                event_stream_message["tool_calls"] = message_chunk.tool_calls
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_calls", event_stream_message)
            elif message_chunk.tool_call_chunks:
                # 工具调用块
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_call_chunks", event_stream_message)
            else:
                # 普通消息块
                yield _make_event("message_chunk", event_stream_message)


def _make_event(event_type: str, data: dict[str, any]):
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"


@app.post("/api/tts")
async def text_to_speech(request: TTSRequest):
    """Convert text to speech using volcengine TTS API."""
    app_id = os.getenv("VOLCENGINE_TTS_APPID", "")
    if not app_id:
        raise HTTPException(status_code=400, detail="VOLCENGINE_TTS_APPID is not set")
    access_token = os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", "")
    if not access_token:
        raise HTTPException(
            status_code=400, detail="VOLCENGINE_TTS_ACCESS_TOKEN is not set"
        )

    try:
        cluster = os.getenv("VOLCENGINE_TTS_CLUSTER", "volcano_tts")
        voice_type = os.getenv("VOLCENGINE_TTS_VOICE_TYPE", "BV700_V2_streaming")

        tts_client = VolcengineTTS(
            appid=app_id,
            access_token=access_token,
            cluster=cluster,
            voice_type=voice_type,
        )
        # Call the TTS API
        result = tts_client.text_to_speech(
            text=request.text[:1024],
            encoding=request.encoding,
            speed_ratio=request.speed_ratio,
            volume_ratio=request.volume_ratio,
            pitch_ratio=request.pitch_ratio,
            text_type=request.text_type,
            with_frontend=request.with_frontend,
            frontend_type=request.frontend_type,
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=str(result["error"]))

        # Decode the base64 audio data
        audio_data = base64.b64decode(result["audio_data"])

        # Return the audio file
        return Response(
            content=audio_data,
            media_type=f"audio/{request.encoding}",
            headers={
                "Content-Disposition": (
                    f"attachment; filename=tts_output.{request.encoding}"
                )
            },
        )

    except Exception as e:
        logger.exception(f"Error in TTS endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.post("/api/podcast/generate")
async def generate_podcast(request: GeneratePodcastRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_podcast_graph()
        final_state = workflow.invoke({"input": report_content})
        audio_bytes = final_state["output"]
        return Response(content=audio_bytes, media_type="audio/mp3")
    except Exception as e:
        logger.exception(f"Error occurred during podcast generation: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.post("/api/ppt/generate")
async def generate_ppt(request: GeneratePPTRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_ppt_graph()
        final_state = workflow.invoke({"input": report_content})
        generated_file_path = final_state["generated_file_path"]
        with open(generated_file_path, "rb") as f:
            ppt_bytes = f.read()
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
        )
    except Exception as e:
        logger.exception(f"Error occurred during ppt generation: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.post("/api/prose/generate")
async def generate_prose(request: GenerateProseRequest):
    try:
        sanitized_prompt = request.prompt.replace("\r\n", "").replace("\n", "")
        logger.info(f"Generating prose for prompt: {sanitized_prompt}")
        workflow = build_prose_graph()
        events = workflow.astream(
            {
                "content": request.prompt,
                "option": request.option,
                "command": request.command,
            },
            stream_mode="messages",
            subgraphs=True,
        )
        return StreamingResponse(
            (f"data: {event[0].content}\n\n" async for _, event in events),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error occurred during prose generation: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.post("/api/prompt/enhance")
async def enhance_prompt(request: EnhancePromptRequest):
    try:
        sanitized_prompt = request.prompt.replace("\r\n", "").replace("\n", "")
        logger.info(f"Enhancing prompt: {sanitized_prompt}")

        # Convert string report_style to ReportStyle enum
        report_style = None
        if request.report_style:
            try:
                # Handle both uppercase and lowercase input
                style_mapping = {
                    "ACADEMIC": ReportStyle.ACADEMIC,
                    "POPULAR_SCIENCE": ReportStyle.POPULAR_SCIENCE,
                    "NEWS": ReportStyle.NEWS,
                    "SOCIAL_MEDIA": ReportStyle.SOCIAL_MEDIA,
                }
                report_style = style_mapping.get(
                    request.report_style.upper(), ReportStyle.ACADEMIC
                )
            except Exception:
                # If invalid style, default to ACADEMIC
                report_style = ReportStyle.ACADEMIC
        else:
            report_style = ReportStyle.ACADEMIC

        workflow = build_prompt_enhancer_graph()
        final_state = workflow.invoke(
            {
                "prompt": request.prompt,
                "context": request.context,
                "report_style": report_style,
            }
        )
        return {"result": final_state["output"]}
    except Exception as e:
        logger.exception(f"Error occurred during prompt enhancement: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.post("/api/mcp/server/metadata", response_model=MCPServerMetadataResponse)
async def mcp_server_metadata(request: MCPServerMetadataRequest):
    """Get information about an MCP server."""
    # Check if MCP server configuration is enabled
    if os.getenv("ENABLE_MCP_SERVER_CONFIGURATION", "false").lower() not in [
        "true",
        "1",
        "yes",
    ]:
        raise HTTPException(
            status_code=403,
            detail="MCP server configuration is disabled. Set ENABLE_MCP_SERVER_CONFIGURATION=true to enable MCP features.",
        )

    try:
        # Set default timeout with a longer value for this endpoint
        timeout = 300  # Default to 300 seconds for this endpoint

        # Use custom timeout from request if provided
        if request.timeout_seconds is not None:
            timeout = request.timeout_seconds

        # Load tools from the MCP server using the utility function
        tools = await load_mcp_tools(
            server_type=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            timeout_seconds=timeout,
        )

        # Create the response with tools
        response = MCPServerMetadataResponse(
            transport=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            tools=tools,
        )

        return response
    except Exception as e:
        logger.exception(f"Error in MCP server metadata endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=INTERNAL_SERVER_ERROR_DETAIL)


@app.get("/api/rag/config", response_model=RAGConfigResponse)
async def rag_config():
    """Get the config of the RAG."""
    return RAGConfigResponse(provider=SELECTED_RAG_PROVIDER)


@app.get("/api/rag/resources", response_model=RAGResourcesResponse)
async def rag_resources(request: Annotated[RAGResourceRequest, Query()]):
    """Get the resources of the RAG."""
    retriever = build_retriever()
    if retriever:
        return RAGResourcesResponse(resources=retriever.list_resources(request.query))
    return RAGResourcesResponse(resources=[])


@app.get("/api/config", response_model=ConfigResponse)
async def config():
    """Get the config of the server."""
    return ConfigResponse(
        rag=RAGConfigResponse(provider=SELECTED_RAG_PROVIDER),
        models=get_configured_llm_models(),
    )
