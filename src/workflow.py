# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
from src.config.configuration import get_recursion_limit
from src.graph import build_graph

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default level is INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)


def enable_debug_logging():
    """Enable debug level logging for more detailed execution information."""
    logging.getLogger("src").setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)

# Create the graph
graph = build_graph()


async def run_agent_workflow_async(
    user_input: str,                          # 用户输入的查询或请求字符串
    debug: bool = False,                      # 是否启用调试模式
    max_plan_iterations: int = 1,             # 最大计划迭代次数
    max_step_num: int = 3,                    # 计划中的最大步骤数
    enable_background_investigation: bool = True,  # 是否启用背景调查
):
    """异步运行代理工作流处理用户输入。
    
    这个函数是工作流的核心，负责:
    1. 初始化工作流状态
    2. 配置运行参数
    3. 处理流式输出
    4. 错误处理和日志记录
    
    Args:
        user_input: 用户的查询或请求内容
        debug: 如果为True，启用调试级别的日志记录
        max_plan_iterations: 计划迭代的最大次数
        max_step_num: 计划中允许的最大步骤数
        enable_background_investigation: 如果为True，在规划前进行网络搜索以增强上下文

    Returns:
        工作流完成后的最终状态
    """
    # 验证输入不能为空
    if not user_input:
        raise ValueError("Input could not be empty")

    # 如果开启调试模式，设置详细日志级别
    if debug:
        enable_debug_logging()

    # 记录工作流开始的日志
    logger.info(f"Starting async workflow with user input: {user_input}")
    
    # 初始化工作流状态
    initial_state = {
        "messages": [{"role": "user", "content": user_input}],  # 初始化消息列表
        "auto_accepted_plan": True,                             # 自动接受计划标志
        "enable_background_investigation": enable_background_investigation,  # 背景调查开关
    }
    
    # 配置工作流参数
    config = {
        "configurable": {
            "thread_id": "default",                # 默认线程ID
            "max_plan_iterations": max_plan_iterations,  # 最大计划迭代次数
            "max_step_num": max_step_num,              # 最大步骤数
            "mcp_settings": {                          # MCP服务器配置
                "servers": {
                    "mcp-github-trending": {          # GitHub趋势分析服务器配置
                        "transport": "stdio",         # 传输方式
                        "command": "uvx",             # 执行命令
                        "args": ["mcp-github-trending"],  # 命令参数
                        "enabled_tools": ["get_github_trending_repositories"],  # 启用的工具
                        "add_to_agents": ["researcher"],  # 分配给的代理
                    }
                }
            },
        },
        "recursion_limit": get_recursion_limit(default=100),  # 设置递归限制
    }
    
    # 用于追踪消息计数的变量
    last_message_cnt = 0
    
    # 异步迭代处理工作流输出
    async for s in graph.astream(
        input=initial_state, config=config, stream_mode="values"
    ):
        try:
            # 处理包含消息的字典输出
            if isinstance(s, dict) and "messages" in s:
                # 跳过重复的消息
                if len(s["messages"]) <= last_message_cnt:
                    continue
                # 更新消息计数
                last_message_cnt = len(s["messages"])
                # 获取最新消息
                message = s["messages"][-1]
                # 根据消息类型选择打印方式
                if isinstance(message, tuple):
                    print(message)
                else:
                    message.pretty_print()
            else:
                # 打印其他类型的输出
                print(f"Output: {s}")
        except Exception as e:
            # 错误处理和日志记录
            logger.error(f"Error processing stream output: {e}")
            print(f"Error processing output: {str(e)}")

    # 记录工作流完成日志
    logger.info("Async workflow completed successfully")

if __name__ == "__main__":
    print(graph.get_graph(xray=True).draw_mermaid())
